# /Users/<USER>/Desktop/aus_job/job_seeker_cli/config/crawler_defaults.py
# 爬虫默认配置：预设的搜索参数和平台配置
# 此文件定义所有爬虫的默认行为，简化用户操作流程

# 默认搜索参数
DEFAULT_SEARCH_PARAMS = {
    "keywords": "product manager",
    "location": "Sydney, Australia",
    "linkedin_pages": 2,
    "seek_pages": 1
}

# LinkedIn配置
LINKEDIN_CONFIG = {
    "results_per_page": 25,
    "headless": False,  # 调试模式：关闭无头浏览器
    "profile_name": "linkedin-browser-profile",
    "timeout": 180,
    "selectors": {
        "job_card": "li.jobs-search-results__list-item",
        "title": ".job-card-list__title",
        "company": ".job-card-container__company-name",
        "location": ".job-card-container__metadata-item",
        "link": "a.job-card-container__link"
    },
    "delay_range": [3, 7]
}

# Seek配置
SEEK_CONFIG = {
    "results_per_page": 100,
    "base_url": "https://www.seek.com.au",
    "timeout": 120,
    "delay_range": [2, 5]
}

# 输出配置
OUTPUT_CONFIG = {
    "input_dir": "data/input",
    "processed_dir": "data/processed",
    "format": "json",
    "include_metadata": True
}
