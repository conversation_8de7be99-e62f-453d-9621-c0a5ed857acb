import json
import os
from pathlib import Path
from typing import Any, List, Optional, Dict

from job_seeker_cli.services.path_manager import PathManager

class FileService:
    """
    A service for handling file operations like reading, writing, and selection.
    """

    def __init__(self, path_manager: PathManager):
        self.path_manager = path_manager

    def ensure_directory_exists(self, directory_name: str) -> bool:
        """
        确保指定目录存在，如果不存在则创建。

        Args:
            directory_name (str): 目录名称

        Returns:
            bool: 成功创建或目录已存在返回True，失败返回False
        """
        try:
            directory_path = self.path_manager.get_path(directory_name)
            os.makedirs(directory_path, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败 '{directory_name}': {e}")
            return False

    def list_files(self, directory_name: str, extension: Optional[str] = None) -> List[str]:
        """
        List files in a given directory.

        Args:
            directory_name (str): The registered name of the directory in PathManager.
            extension (Optional[str]): Filter by file extension (e.g., '.json').

        Returns:
            List[str]: A list of file names.
        """
        directory_path = self.path_manager.get_path(directory_name)
        files = os.listdir(directory_path)
        if extension:
            return [f for f in files if f.endswith(extension)]
        return files

    def select_file(self, directory_name: str, prompt: str, extension: Optional[str] = None) -> Optional[str]:
        """
        Prompts the user to select a file from a directory.

        Args:
            directory_name (str): The registered name of the directory.
            prompt (str): The prompt to display to the user.
            extension (Optional[str]): Filter files by extension.

        Returns:
            Optional[str]: The name of the selected file, or None if canceled.
        """
        files = self.list_files(directory_name, extension)
        if not files:
            print(f"No files found in '{directory_name}'.")
            return None

        print(prompt)
        for i, filename in enumerate(files):
            print(f"{i + 1}: {filename}")

        while True:
            try:
                choice = input("Enter the number of the file to select (or 0 to cancel): ")
                choice_num = int(choice)
                if 0 < choice_num <= len(files):
                    return files[choice_num - 1]
                if choice_num == 0:
                    return None
                print("Invalid number. Please try again.")
            except ValueError:
                print("Invalid input. Please enter a number.")

    def read_json(self, directory_name: str, filename: str) -> Optional[Dict[str, Any]]:
        """
        Reads a JSON file from a specified directory.

        Args:
            directory_name (str): The registered name of the directory.
            filename (str): The name of the file.

        Returns:
            Optional[Dict[str, Any]]: The content of the JSON file, or None on error.
        """
        file_path = self.path_manager.get_path(directory_name) / filename
        if not file_path.exists():
            print(f"Error: File '{filename}' not found in '{directory_name}'.")
            return None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error reading or parsing JSON file '{filename}': {e}")
            return None

    def write_json(self, directory_name: str, filename: str, data: Dict[str, Any]):
        """
        Writes data to a JSON file in a specified directory.

        Args:
            directory_name (str): The registered name of the directory.
            filename (str): The name of the file.
            data (Dict[str, Any]): The data to write.
        """
        file_path = self.path_manager.get_path(directory_name) / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            print(f"Successfully wrote to '{file_path}'")
        except IOError as e:
            print(f"Error writing to JSON file '{filename}': {e}")

    def read_file(self, directory_name: str, filename: str) -> Optional[str]:
        """
        Reads a text file from a specified directory.

        Args:
            directory_name (str): The registered name of the directory.
            filename (str): The name of the file.

        Returns:
            Optional[str]: The content of the file, or None on error.
        """
        file_path = self.path_manager.get_path(directory_name) / filename
        if not file_path.exists():
            print(f"Error: File '{filename}' not found in '{directory_name}'.")
            return None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except IOError as e:
            print(f"Error reading file '{filename}': {e}")
            return None

    def write_file(self, directory_name: str, filename: str, content: str):
        """
        Writes content to a text file in a specified directory.

        Args:
            directory_name (str): The registered name of the directory.
            filename (str): The name of the file.
            content (str): The content to write.
        """
        file_path = self.path_manager.get_path(directory_name) / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Successfully wrote to '{file_path}'")
        except IOError as e:
            print(f"Error writing to file '{filename}': {e}")

if __name__ == '__main__':
    pm = PathManager()
    fs = FileService(pm)

    # Example usage
    # Create a dummy file for testing
    fs.write_json("input", "test.json", {"key": "value"})
    
    files = fs.list_files("input", ".json")
    print("JSON files in input directory:", files)

    content = fs.read_json("input", "test.json")
    print("Content of test.json:", content)

    selected = fs.select_file("input", "Please select a test file", ".json")
    if selected:
        print(f"You selected: {selected}")
    else:
        print("Selection canceled.")
