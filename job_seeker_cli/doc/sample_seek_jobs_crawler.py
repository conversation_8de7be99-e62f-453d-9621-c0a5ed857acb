# tools/seek_jobs_crawler.py
# 该脚本旨在通过基于身份的爬虫，从澳大利亚招聘网站Seek.com.au上抓取职位列表。
# 该脚本的主要功能是模拟用户登录，并从指定的搜索结果页面中提取职位信息，包括职位名称、公司、地点和职位链接。


import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, BrowserProfiler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from crawl4ai.async_configs import CacheMode

# 常量
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "seek_results")
os.makedirs(OUTPUT_DIR, exist_ok=True)

class SeekJobsCrawler:
    """
    Seek.com.au职位爬虫，使用基于身份的爬取方式
    以真实用户的身份访问内容。
    """
    
    def __init__(
        self,
        search_url: str,
        output_dir: str = OUTPUT_DIR,
        profile_name: str = "seek-browser-profile",
        headless: bool = False
    ):
        self.search_url = search_url
        self.output_dir = output_dir
        self.profile_name = profile_name
        self.headless = headless
        
    async def setup_browser_profile(self) -> str:
        """为Seek设置浏览器配置文件，如果不存在则创建一个。"""
        print("为Seek.com.au设置浏览器配置文件...")
        
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        profiles_dir = os.path.join(project_root, "browser_profiles")
        os.makedirs(profiles_dir, exist_ok=True)
        
        profiler = BrowserProfiler()
        
        existing_profiles = profiler.list_profiles()
        profile_path = None
        
        for profile in existing_profiles:
            if profile['name'] == self.profile_name:
                profile_path = profile['path']
                print(f"使用现有配置文件: {self.profile_name} 位于 {profile_path}")
                break
        
        if not profile_path:
            print(f"创建新的浏览器配置文件: {self.profile_name} 位于 {profiles_dir}")
            print("请在打开的浏览器窗口中登录Seek.com.au...")
            print("完成后，在终端按'q'保存配置文件。")
            
            os.environ["CRAWL4AI_PROFILES_DIR"] = profiles_dir
            profile_path = await profiler.create_profile(
                profile_name=self.profile_name
            )
            
            print(f"配置文件保存在: {profile_path}")
        
        return profile_path
    
    async def extract_job_info(self) -> List[Dict[str, Any]]:
        """
        从Seek职位搜索结果中提取职位信息。
        """
        profile_path = await self.setup_browser_profile()
        
        browser_config = BrowserConfig(
            headless=self.headless,
            use_managed_browser=True,
            user_data_dir=profile_path,
            browser_type="chromium",
            verbose=True
        )
        
        # 定义Seek职位列表的提取模式
        # 基于 mock.json 的格式
        schema = {
            "name": "Seek Job Listings",
            "baseSelector": "div[data-search-sol-meta]",
            "fields": [
                {
                    "name": "jobTitle",
                    "selector": '[data-automation="jobTitle"]',
                    "type": "text"
                },
                {
                    "name": "jobAdvertiser",
                    "selector": '[data-automation="jobAdvertiser"]',
                    "type": "text"
                },
                {
                    "name": "recommendedJobLink",
                    "selector": 'a[data-automation^="recommendedJobLink"]',
                    "type": "attribute",
                    "attribute": "href"
                }
            ]
        }
        
        # 爬虫配置，无需滚动或懒加载处理
        crawl_config = CrawlerRunConfig(
            wait_for='div[data-search-sol-meta]', # 等待职位卡片加载
            page_timeout=120000,  # 2分钟页面超时
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(schema, verbose=True),
            verbose=True
        )
        
        print(f"开始从以下链接爬取Seek职位: {self.search_url}")
        
        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=self.search_url, config=crawl_config)
                
                if not result.success:
                    print(f"爬取 {self.search_url} 时出错: {result.error_message}")
                    return []
                
                print("成功加载页面。正在提取职位信息...")
                
                if not hasattr(result, 'extracted_content') or not result.extracted_content:
                    print("未找到提取内容。提取可能失败。")
                    # 保存HTML以便检查
                    debug_file = os.path.join(self.output_dir, "seek_debug.html")
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(result.html)
                    print(f"已将HTML内容保存至 {debug_file} 用于调试")
                    return []
                
                try:
                    job_data = json.loads(result.extracted_content)
                    
                    for job in job_data:
                        if job.get('recommendedJobLink') and job['recommendedJobLink'].startswith('/'):
                            job['recommendedJobLink'] = f"https://www.seek.com.au{job['recommendedJobLink']}"
                    
                    print(f"成功提取 {len(job_data)} 个职位列表")
                    return job_data
                except json.JSONDecodeError as e:
                    print(f"解析提取内容时出错: {e}")
                    print(f"原始提取内容: {result.extracted_content[:200]}...")
                    return []
        except Exception as e:
            print(f"爬取过程中出错: {e}")
            return []
    
    async def save_results(self, jobs: List[Dict[str, Any]]):
        """保存提取的职位信息到JSON文件。"""
        if not jobs:
            print("没有职位可保存。")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"seek_jobs_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        print(f"已保存 {len(jobs)} 个职位列表到 {filepath}")
        
    async def run(self):
        """运行Seek职位爬虫并保存结果。"""
        print(f"🚀 开始爬取任务...")
        jobs = await self.extract_job_info()
        
        print(f"\n--- 🎉 爬取任务完成！---")
        print(f"总共提取到 {len(jobs)} 条职位信息。")
        await self.save_results(jobs)
        return jobs

async def main():
    """运行Seek职位爬虫的主函数。"""
    # 示例URL：在悉尼搜索 "Software Engineer"
    search_url = "https://www.seek.com.au/"
    
    crawler = SeekJobsCrawler(
        search_url=search_url,
        headless=False # 设置为False可以看到浏览器，方便首次登录
    )
    
    jobs = await crawler.run()
    print(f"爬取完成。找到 {len(jobs)} 个职位列表。")

if __name__ == "__main__":
    asyncio.run(main())
