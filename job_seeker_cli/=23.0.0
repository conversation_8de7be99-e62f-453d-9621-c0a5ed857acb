Collecting crawl4ai
  Downloading crawl4ai-0.6.3-py3-none-any.whl.metadata (36 kB)
Collecting aiofiles
  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting aiosqlite~=0.20 (from crawl4ai)
  Downloading aiosqlite-0.21.0-py3-none-any.whl.metadata (4.3 kB)
Collecting lxml~=5.3 (from crawl4ai)
  Downloading lxml-5.4.0-cp313-cp313-macosx_10_13_universal2.whl.metadata (3.5 kB)
Collecting litellm>=1.53.1 (from crawl4ai)
  Downloading litellm-1.73.2-py3-none-any.whl.metadata (39 kB)
Collecting numpy<3,>=1.26.0 (from crawl4ai)
  Downloading numpy-2.3.1-cp313-cp313-macosx_14_0_arm64.whl.metadata (62 kB)
Collecting pillow~=10.4 (from crawl4ai)
  Downloading pillow-10.4.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (9.2 kB)
Collecting playwright>=1.49.0 (from crawl4ai)
  Downloading playwright-1.53.0-py3-none-macosx_11_0_arm64.whl.metadata (3.5 kB)
Requirement already satisfied: python-dotenv~=1.0 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (1.1.1)
Requirement already satisfied: requests~=2.26 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (2.32.4)
Requirement already satisfied: beautifulsoup4~=4.12 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (4.13.4)
Collecting tf-playwright-stealth>=1.1.0 (from crawl4ai)
  Downloading tf_playwright_stealth-1.2.0-py3-none-any.whl.metadata (2.5 kB)
Collecting xxhash~=3.4 (from crawl4ai)
  Downloading xxhash-3.5.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (12 kB)
Collecting rank-bm25~=0.2 (from crawl4ai)
  Downloading rank_bm25-0.2.2-py3-none-any.whl.metadata (3.2 kB)
Collecting colorama~=0.4 (from crawl4ai)
  Using cached colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Collecting snowballstemmer~=2.2 (from crawl4ai)
  Downloading snowballstemmer-2.2.0-py2.py3-none-any.whl.metadata (6.5 kB)
Collecting pydantic>=2.10 (from crawl4ai)
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting pyOpenSSL>=24.3.0 (from crawl4ai)
  Downloading pyopenssl-25.1.0-py3-none-any.whl.metadata (17 kB)
Requirement already satisfied: psutil>=6.1.1 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (7.0.0)
Collecting nltk>=3.9.1 (from crawl4ai)
  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: rich>=13.9.4 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (14.0.0)
Collecting cssselect>=1.2.0 (from crawl4ai)
  Downloading cssselect-1.3.0-py3-none-any.whl.metadata (2.6 kB)
Collecting httpx>=0.27.2 (from crawl4ai)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting fake-useragent>=2.0.3 (from crawl4ai)
  Downloading fake_useragent-2.2.0-py3-none-any.whl.metadata (17 kB)
Requirement already satisfied: click>=8.1.7 in ./.venv/lib/python3.13/site-packages (from crawl4ai) (8.2.1)
Collecting pyperclip>=1.8.2 (from crawl4ai)
  Downloading pyperclip-1.9.0.tar.gz (20 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting chardet>=5.2.0 (from crawl4ai)
  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)
Collecting aiohttp>=3.11.11 (from crawl4ai)
  Downloading aiohttp-3.12.13-cp313-cp313-macosx_11_0_arm64.whl.metadata (7.6 kB)
Collecting brotli>=1.1.0 (from crawl4ai)
  Downloading Brotli-1.1.0-cp313-cp313-macosx_10_13_universal2.whl.metadata (5.5 kB)
Collecting humanize>=4.10.0 (from crawl4ai)
  Downloading humanize-4.12.3-py3-none-any.whl.metadata (7.8 kB)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp>=3.11.11->crawl4ai)
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp>=3.11.11->crawl4ai)
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Requirement already satisfied: attrs>=17.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp>=3.11.11->crawl4ai) (25.3.0)
Collecting frozenlist>=1.1.1 (from aiohttp>=3.11.11->crawl4ai)
  Downloading frozenlist-1.7.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (18 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp>=3.11.11->crawl4ai)
  Downloading multidict-6.5.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (5.3 kB)
Collecting propcache>=0.2.0 (from aiohttp>=3.11.11->crawl4ai)
  Downloading propcache-0.3.2-cp313-cp313-macosx_11_0_arm64.whl.metadata (12 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp>=3.11.11->crawl4ai)
  Downloading yarl-1.20.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (73 kB)
Requirement already satisfied: typing_extensions>=4.0 in ./.venv/lib/python3.13/site-packages (from aiosqlite~=0.20->crawl4ai) (4.13.2)
Requirement already satisfied: soupsieve>1.2 in ./.venv/lib/python3.13/site-packages (from beautifulsoup4~=4.12->crawl4ai) (2.7)
Collecting anyio (from httpx>=0.27.2->crawl4ai)
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Requirement already satisfied: certifi in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.2->crawl4ai) (2025.6.15)
Collecting httpcore==1.* (from httpx>=0.27.2->crawl4ai)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Requirement already satisfied: idna in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.2->crawl4ai) (3.10)
Requirement already satisfied: h11>=0.16 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.27.2->crawl4ai) (0.16.0)
Collecting importlib-metadata>=6.8.0 (from litellm>=1.53.1->crawl4ai)
  Downloading importlib_metadata-8.7.0-py3-none-any.whl.metadata (4.8 kB)
Collecting jinja2<4.0.0,>=3.1.2 (from litellm>=1.53.1->crawl4ai)
  Downloading jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting jsonschema<5.0.0,>=4.22.0 (from litellm>=1.53.1->crawl4ai)
  Downloading jsonschema-4.24.0-py3-none-any.whl.metadata (7.8 kB)
Collecting openai>=1.68.2 (from litellm>=1.53.1->crawl4ai)
  Downloading openai-1.92.2-py3-none-any.whl.metadata (29 kB)
Collecting tiktoken>=0.7.0 (from litellm>=1.53.1->crawl4ai)
  Downloading tiktoken-0.9.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (6.7 kB)
Collecting tokenizers (from litellm>=1.53.1->crawl4ai)
  Downloading tokenizers-0.21.2-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting joblib (from nltk>=3.9.1->crawl4ai)
  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)
Collecting regex>=2021.8.3 (from nltk>=3.9.1->crawl4ai)
  Downloading regex-2024.11.6-cp313-cp313-macosx_11_0_arm64.whl.metadata (40 kB)
Collecting tqdm (from nltk>=3.9.1->crawl4ai)
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting pyee<14,>=13 (from playwright>=1.49.0->crawl4ai)
  Downloading pyee-13.0.0-py3-none-any.whl.metadata (2.9 kB)
Collecting greenlet<4.0.0,>=3.1.1 (from playwright>=1.49.0->crawl4ai)
  Downloading greenlet-3.2.3-cp313-cp313-macosx_11_0_universal2.whl.metadata (4.1 kB)
Collecting annotated-types>=0.6.0 (from pydantic>=2.10->crawl4ai)
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic>=2.10->crawl4ai)
  Downloading pydantic_core-2.33.2-cp313-cp313-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting typing-inspection>=0.4.0 (from pydantic>=2.10->crawl4ai)
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting cryptography<46,>=41.0.5 (from pyOpenSSL>=24.3.0->crawl4ai)
  Downloading cryptography-45.0.4-cp311-abi3-macosx_10_9_universal2.whl.metadata (5.7 kB)
Requirement already satisfied: charset_normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests~=2.26->crawl4ai) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests~=2.26->crawl4ai) (2.4.0)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.13/site-packages (from rich>=13.9.4->crawl4ai) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.13/site-packages (from rich>=13.9.4->crawl4ai) (2.19.1)
Collecting fake-http-header<0.4.0,>=0.3.5 (from tf-playwright-stealth>=1.1.0->crawl4ai)
  Downloading fake_http_header-0.3.5-py3-none-any.whl.metadata (3.5 kB)
Collecting cffi>=1.14 (from cryptography<46,>=41.0.5->pyOpenSSL>=24.3.0->crawl4ai)
  Using cached cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (1.5 kB)
Collecting zipp>=3.20 (from importlib-metadata>=6.8.0->litellm>=1.53.1->crawl4ai)
  Downloading zipp-3.23.0-py3-none-any.whl.metadata (3.6 kB)
Collecting MarkupSafe>=2.0 (from jinja2<4.0.0,>=3.1.2->litellm>=1.53.1->crawl4ai)
  Downloading MarkupSafe-3.0.2-cp313-cp313-macosx_11_0_arm64.whl.metadata (4.0 kB)
Collecting jsonschema-specifications>=2023.03.6 (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai)
  Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl.metadata (2.9 kB)
Collecting referencing>=0.28.4 (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai)
  Downloading referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)
Collecting rpds-py>=0.7.1 (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai)
  Downloading rpds_py-0.25.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (4.1 kB)
Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=13.9.4->crawl4ai) (0.1.2)
Collecting distro<2,>=1.7.0 (from openai>=1.68.2->litellm>=1.53.1->crawl4ai)
  Downloading distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting jiter<1,>=0.4.0 (from openai>=1.68.2->litellm>=1.53.1->crawl4ai)
  Downloading jiter-0.10.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (5.2 kB)
Requirement already satisfied: sniffio in ./.venv/lib/python3.13/site-packages (from openai>=1.68.2->litellm>=1.53.1->crawl4ai) (1.3.1)
Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers->litellm>=1.53.1->crawl4ai)
  Downloading huggingface_hub-0.33.1-py3-none-any.whl.metadata (14 kB)
Collecting pycparser (from cffi>=1.14->cryptography<46,>=41.0.5->pyOpenSSL>=24.3.0->crawl4ai)
  Using cached pycparser-2.22-py3-none-any.whl.metadata (943 bytes)
Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm>=1.53.1->crawl4ai)
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm>=1.53.1->crawl4ai)
  Downloading fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: packaging>=20.9 in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm>=1.53.1->crawl4ai) (25.0)
Requirement already satisfied: pyyaml>=5.1 in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm>=1.53.1->crawl4ai) (6.0.2)
Collecting hf-xet<2.0.0,>=1.1.2 (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm>=1.53.1->crawl4ai)
  Downloading hf_xet-1.1.5-cp37-abi3-macosx_11_0_arm64.whl.metadata (879 bytes)
Downloading crawl4ai-0.6.3-py3-none-any.whl (292 kB)
Downloading aiofiles-24.1.0-py3-none-any.whl (15 kB)
Downloading aiohttp-3.12.13-cp313-cp313-macosx_11_0_arm64.whl (464 kB)
Downloading aiosqlite-0.21.0-py3-none-any.whl (15 kB)
Downloading Brotli-1.1.0-cp313-cp313-macosx_10_13_universal2.whl (815 kB)
   ━━━━━━━━━━━━━━━━━━━ 815.7/815.7 kB 3.2 MB/s eta 0:00:00
Downloading chardet-5.2.0-py3-none-any.whl (199 kB)
Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Downloading cssselect-1.3.0-py3-none-any.whl (18 kB)
Downloading fake_useragent-2.2.0-py3-none-any.whl (161 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Downloading humanize-4.12.3-py3-none-any.whl (128 kB)
Downloading litellm-1.73.2-py3-none-any.whl (8.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━ 8.4/8.4 MB 2.3 MB/s eta 0:00:00
Downloading lxml-5.4.0-cp313-cp313-macosx_10_13_universal2.whl (8.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 8.1/8.1 MB 2.9 MB/s eta 0:00:00
Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 1.5/1.5 MB 1.8 MB/s eta 0:00:00
Downloading numpy-2.3.1-cp313-cp313-macosx_14_0_arm64.whl (5.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 5.1/5.1 MB 2.1 MB/s eta 0:00:00
Downloading pillow-10.4.0-cp313-cp313-macosx_11_0_arm64.whl (3.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 3.4/3.4 MB 2.7 MB/s eta 0:00:00
Downloading playwright-1.53.0-py3-none-macosx_11_0_arm64.whl (38.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━ 38.6/38.6 MB 2.3 MB/s eta 0:00:00
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
Downloading pydantic_core-2.33.2-cp313-cp313-macosx_11_0_arm64.whl (1.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 1.8/1.8 MB 2.5 MB/s eta 0:00:00
Downloading pyopenssl-25.1.0-py3-none-any.whl (56 kB)
Downloading rank_bm25-0.2.2-py3-none-any.whl (8.6 kB)
Downloading snowballstemmer-2.2.0-py2.py3-none-any.whl (93 kB)
Downloading tf_playwright_stealth-1.2.0-py3-none-any.whl (33 kB)
Downloading xxhash-3.5.0-cp313-cp313-macosx_11_0_arm64.whl (30 kB)
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading cryptography-45.0.4-cp311-abi3-macosx_10_9_universal2.whl (7.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 7.1/7.1 MB 2.2 MB/s eta 0:00:00
Downloading fake_http_header-0.3.5-py3-none-any.whl (14 kB)
Downloading frozenlist-1.7.0-cp313-cp313-macosx_11_0_arm64.whl (45 kB)
Downloading greenlet-3.2.3-cp313-cp313-macosx_11_0_universal2.whl (270 kB)
Downloading importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading jsonschema-4.24.0-py3-none-any.whl (88 kB)
Downloading multidict-6.5.1-cp313-cp313-macosx_11_0_arm64.whl (42 kB)
Downloading openai-1.92.2-py3-none-any.whl (753 kB)
   ━━━━━━━━━━━━━━━━━━━━━ 753.3/753.3 kB 1.9 MB/s eta 0:00:00
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Downloading propcache-0.3.2-cp313-cp313-macosx_11_0_arm64.whl (41 kB)
Downloading pyee-13.0.0-py3-none-any.whl (15 kB)
Downloading regex-2024.11.6-cp313-cp313-macosx_11_0_arm64.whl (284 kB)
Downloading tiktoken-0.9.0-cp313-cp313-macosx_11_0_arm64.whl (1.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 1.0/1.0 MB 2.4 MB/s eta 0:00:00
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Downloading yarl-1.20.1-cp313-cp313-macosx_11_0_arm64.whl (88 kB)
Downloading joblib-1.5.1-py3-none-any.whl (307 kB)
Downloading tokenizers-0.21.2-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 2.7/2.7 MB 2.5 MB/s eta 0:00:00
Using cached cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl (178 kB)
Downloading distro-1.9.0-py3-none-any.whl (20 kB)
Downloading huggingface_hub-0.33.1-py3-none-any.whl (515 kB)
Downloading jiter-0.10.0-cp313-cp313-macosx_11_0_arm64.whl (318 kB)
Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)
Downloading MarkupSafe-3.0.2-cp313-cp313-macosx_11_0_arm64.whl (12 kB)
Downloading referencing-0.36.2-py3-none-any.whl (26 kB)
Downloading rpds_py-0.25.1-cp313-cp313-macosx_11_0_arm64.whl (350 kB)
Downloading zipp-3.23.0-py3-none-any.whl (10 kB)
Downloading fsspec-2025.5.1-py3-none-any.whl (199 kB)
Downloading hf_xet-1.1.5-cp37-abi3-macosx_11_0_arm64.whl (2.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━ 2.6/2.6 MB 2.7 MB/s eta 0:00:00
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached pycparser-2.22-py3-none-any.whl (117 kB)
Building wheels for collected packages: pyperclip
  Building wheel for pyperclip (pyproject.toml): started
  Building wheel for pyperclip (pyproject.toml): finished with status 'done'
  Created wheel for pyperclip: filename=pyperclip-1.9.0-py3-none-any.whl size=11103 sha256=53b160d0f5596056bc3ca178c8c208d6885a751e5f7c67b7de816fd7ae8230f5
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/9c/79/90/c9e831caaffa2633ad99f1d35c6ea39866b92de62e909e89ef
Successfully built pyperclip
Installing collected packages: snowballstemmer, pyperclip, brotli, zipp, xxhash, typing-inspection, tqdm, rpds-py, regex, pyee, pydantic-core, pycparser, propcache, pillow, numpy, multidict, MarkupSafe, lxml, joblib, jiter, humanize, httpcore, hf-xet, greenlet, fsspec, frozenlist, filelock, fake-useragent, fake-http-header, distro, cssselect, colorama, chardet, anyio, annotated-types, aiosqlite, aiohappyeyeballs, aiofiles, yarl, tiktoken, referencing, rank-bm25, pydantic, playwright, nltk, jinja2, importlib-metadata, huggingface-hub, httpx, cffi, aiosignal, tokenizers, tf-playwright-stealth, openai, jsonschema-specifications, cryptography, aiohttp, pyOpenSSL, jsonschema, litellm, crawl4ai
Successfully installed MarkupSafe-3.0.2 aiofiles-24.1.0 aiohappyeyeballs-2.6.1 aiohttp-3.12.13 aiosignal-1.3.2 aiosqlite-0.21.0 annotated-types-0.7.0 anyio-4.9.0 brotli-1.1.0 cffi-1.17.1 chardet-5.2.0 colorama-0.4.6 crawl4ai-0.6.3 cryptography-45.0.4 cssselect-1.3.0 distro-1.9.0 fake-http-header-0.3.5 fake-useragent-2.2.0 filelock-3.18.0 frozenlist-1.7.0 fsspec-2025.5.1 greenlet-3.2.3 hf-xet-1.1.5 httpcore-1.0.9 httpx-0.28.1 huggingface-hub-0.33.1 humanize-4.12.3 importlib-metadata-8.7.0 jinja2-3.1.6 jiter-0.10.0 joblib-1.5.1 jsonschema-4.24.0 jsonschema-specifications-2025.4.1 litellm-1.73.2 lxml-5.4.0 multidict-6.5.1 nltk-3.9.1 numpy-2.3.1 openai-1.92.2 pillow-10.4.0 playwright-1.53.0 propcache-0.3.2 pyOpenSSL-25.1.0 pycparser-2.22 pydantic-2.11.7 pydantic-core-2.33.2 pyee-13.0.0 pyperclip-1.9.0 rank-bm25-0.2.2 referencing-0.36.2 regex-2024.11.6 rpds-py-0.25.1 snowballstemmer-2.2.0 tf-playwright-stealth-1.2.0 tiktoken-0.9.0 tokenizers-0.21.2 tqdm-4.67.1 typing-inspection-0.4.1 xxhash-3.5.0 yarl-1.20.1 zipp-3.23.0
