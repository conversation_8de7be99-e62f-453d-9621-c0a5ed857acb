## Commands

### Installation
`pip install -r requirements.txt`

### Running the Application

**Main Workflow (Interactive Menu):**
```bash
python menu_system.py
```
> **Note:** This is the recommended way to run the application, as it provides a guided user experience.

**Specific Workflows:**
You can also run individual workflows directly. This is useful for testing or automating specific parts of the process.

- **Web Scraping (e.g., LinkedIn):**
  ```bash
  # Note: This might require additional setup or command-line arguments.
  python -m workflows.web_scraping --platform linkedin
  ```
- **Job Analysis:**
  ```bash
  python -m workflows.job_analysis
  ```
- **Cover Letter Generation:**
  ```bash
  python -m workflows.cover_letter
  ```

### Testing
- **Run all tests:** `pytest`
- **Run tests in a specific file:** `pytest tests/unit/test_data_manager.py`
- **Run a specific test:** `pytest tests/unit/test_data_manager.py::TestClassName::test_method_name`

## Code Style
- **Formatting**: Follows PEP 8. Use a linter like `flake8` or `pylint`.
- **Imports**: Use absolute imports. Group imports in the order: standard library, third-party packages, local application imports.
- **Naming**: `snake_case` for variables and functions, `PascalCase` for classes.
- **Types**: Use type hints for all function signatures.
- **Error Handling**: Use try-except blocks for operations that can fail (e.g., file I/O, network requests).
- **Docstrings**: Use docstrings to explain the purpose of modules, classes, and functions.

## Project Architecture Overview

The project is architected in a modular and service-oriented manner, separating concerns into distinct layers.

- **`menu_system.py`**: The main entry point for the interactive CLI. It orchestrates the different workflows.
- **`workflows/`**: Defines the high-level business logic. Each file represents a major step in the job application process (e.g., `web_scraping.py`, `job_analysis.py`).
- **`services/`**: Provides core, reusable services like `FileService`, `PathManager`, and `ApiClient`. These services abstract away low-level details.
- **`scripts/`**: Contains the "engine" of the application. This directory is further subdivided by function:
    - **`crawlers/`**: Holds the web scraping logic for different job platforms (e.g., `linkedin_crawler.py`, `seek_crawler.py`).
    - **`fetchers/`**: Contains utilities to fetch detailed job information from scraped data.
    - **`analyzers/`**: Includes the logic for AI-powered job-to-resume matching (`job_analyzer.py`).
    - **`generators/`**: Responsible for generating cover letters (`letter_generator.py`).
- **`ui/`**: Manages all user interaction, including menu rendering and input prompts.
- **`utils/`**: A new directory for shared utility functions, such as `platform_utils.py`, which helps in handling platform-specific logic.
- **`data/`**: The central repository for all data, including user's personal info, input job lists, processed data, and final targets.
- **`output/`**: Where the final generated files, like cover letters, are stored.
- **`config/`**: For application-wide configuration, such as default settings for crawlers.
