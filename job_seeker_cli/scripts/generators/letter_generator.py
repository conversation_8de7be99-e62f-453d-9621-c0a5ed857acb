import json
import requests
import argparse
import os
from typing import List, Dict, Any, Optional
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console

from job_seeker_cli.services.path_manager import PathManager
from job_seeker_cli.services.file_service import FileService

from job_seeker_cli.services.api_client import ApiClient

class LetterGenerator:
    def __init__(self, file_service: FileService, api_client: ApiClient):
        self.file_service = file_service
        self.api_client = api_client
        self.console = Console()

    def _generate_letter_content(self, prompt: str) -> str:
        """Calls the API to generate the cover letter content."""
        body = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'system', 'content': 'You are an expert in writing cover letters...'}, # Truncated for brevity
                {'role': 'user', 'content': prompt}
            ],
            'temperature': 0.7
        }
        data = self.api_client.post(body)
        if data:
            return data.get('choices', [{}])[0].get('message', {}).get('content', '')
        return ""

    def generate_letters(self, jobs_filename: str, resume_filename: str, prompt_filename: str, guide_filename: str):
        """
        Generates cover letters for a list of target jobs.

        Args:
            jobs_filename (str): The name of the target jobs JSON file.
            resume_filename (str): The name of the resume file.
            prompt_filename (str): The name of the cover letter prompt template file.
            guide_filename (str): The name of the guide file.
        """
        jobs: Optional[List[Dict[str, Any]]] = self.file_service.read_json("target_jobs", jobs_filename)
        if not jobs:
            self.console.print(f"[bold red]Could not read or parse {jobs_filename}[/bold red]")
            return

        resume_content = self.file_service.read_file("personal", resume_filename)
        if not resume_content:
            self.console.print(f"[bold red]Could not read resume file {resume_filename}[/bold red]")
            return
            
        prompt_template = self.file_service.read_file("personal", prompt_filename)
        if not prompt_template:
            self.console.print(f"[bold red]Could not read prompt template file {prompt_filename}[/bold red]")
            return

        # 读取guide_content文件
        guide_content = self.file_service.read_file("personal", guide_filename)
        if not guide_content:
            self.console.print(f"[yellow]Warning: Could not read {guide_filename}, using empty guide content[/yellow]")
            guide_content = ""

        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(bar_width=40),
            TextColumn("[bold green]{task.completed}/{task.total}[/bold green]"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task("[cyan]Generating cover letters[/cyan]", total=len(jobs))
            for i, job in enumerate(jobs):
                job_title = job.get('jobTitle', 'N/A')
                progress.update(task, description=f"[cyan]Generating letter for: {job_title}[/cyan]")
                
                job_details = job.get("jobDetails", "")
                
                # 使用模板替换机制，正确替换占位符
                prompt = prompt_template.format(
                    job_details=job_details,
                    cv_content=resume_content,
                    guide_content=guide_content
                )

                cover_text = self._generate_letter_content(prompt)
                if not cover_text:
                    progress.log(f"[red]  -> Failed to generate cover letter for {job_title}.[/red]")
                    progress.update(task, advance=1)
                    continue

                advertiser = job.get("jobAdvertiser", "company").replace("/", "_")
                output_filename = f"{advertiser}.md"
                
                file_content = (f"# {advertiser}\n\n"
                                f"## Job Info\n{json.dumps(job, indent=4)}\n\n"
                                f"## Cover Letter\n{cover_text}")
                
                self.file_service.write_file("cover_letters", output_filename, file_content)
                progress.log(f"  -> Cover letter for {job_title} saved to 'cover_letters/{output_filename}'")
                progress.update(task, advance=1)

def main():
    parser = argparse.ArgumentParser(description="Generate cover letters for target jobs.")
    parser.add_argument('--jobs', type=str, required=True, help='Target jobs JSON file name (e.g., target_jobs_0619.json)')
    parser.add_argument('--resume', type=str, default='qiansui_cv.md', help='Resume file name in personal directory.')
    parser.add_argument('--prompt', type=str, default='coverletter-prompt.md', help='Prompt template file name in personal directory.')
    parser.add_argument('--guide', type=str, default='coverletter-guide.md', help='Guide file name in personal directory.')
    args = parser.parse_args()

    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        raise RuntimeError('DEEPSEEK_API_KEY not set. Please define it in .env or environment variables.')

    path_manager = PathManager()
    file_service = FileService(path_manager)
    api_client = ApiClient(api_key=api_key, api_url='https://api.deepseek.com/chat/completions')
    generator = LetterGenerator(file_service, api_client)
    
    generator.generate_letters(args.jobs, args.resume, args.prompt, args.guide)

if __name__ == "__main__":
    # Example: DEEPSEEK_API_KEY="your_key" python job_seeker_cli/scripts/letter_generator.py --jobs target_jobs_0619.json
    main()
