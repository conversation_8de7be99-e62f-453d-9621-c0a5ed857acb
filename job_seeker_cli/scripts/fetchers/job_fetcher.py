import json
import requests
from bs4 import BeautifulSoup
import re
import argparse
from typing import List, Dict, Any, Optional
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console

from job_seeker_cli.services.path_manager import <PERSON>Manager
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.utils.platform_utils import detect_platform, analyze_job_file, get_platform_emoji
from job_seeker_cli.scripts.fetchers.linkedin_utils import LinkedInFetcher
from job_seeker_cli.scripts.fetchers.seek_utils import SeekFetcher

# Common headers to mimic browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                  'AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/********* Safari/537.36',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Referer': 'https://www.seek.com.au',
    'Origin': 'https://www.seek.com.au'
}

def clean_unicode_text(text: str) -> str:
    """Clean text of invalid unicode characters."""
    if not isinstance(text, str):
        return text
    return re.sub(r'[\ud800-\udfff]', '', text)

class JobFetcher:
    def __init__(self, file_service: FileService):
        self.file_service = file_service
        self.console = Console()
        self.linkedin_fetcher = LinkedInFetcher()
        self.seek_fetcher = SeekFetcher()

    def fetch_job_details(self, input_filename: str, output_filename: str):
        """
        Enhanced job details fetching with multi-platform support

        Args:
            input_filename (str): The name of the input JSON file in the 'input' directory.
            output_filename (str): The name for the output JSON file in the 'processed' directory.
        """
        jobs: Optional[List[Dict[str, Any]]] = self.file_service.read_json("input", input_filename)
        if not jobs:
            self.console.print("[bold red]Could not read or parse [/bold red]" + input_filename)
            return

        # Analyze platform distribution
        platform_stats = analyze_job_file(jobs)
        self._display_platform_analysis(platform_stats)

        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(bar_width=40),
            TextColumn("[bold green]{task.completed}/{task.total}[/bold green]"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            main_task = progress.add_task("[cyan]抓取职位详情[/cyan]", total=len(jobs))

            try:
                for i, job in enumerate(jobs):
                    job_title = job.get('jobTitle', 'N/A')
                    url = job.get('recommendedJobLink')

                    if not url:
                        progress.log(f"[yellow]职位 {i+1} 缺少链接。跳过。[/yellow]")
                        job['jobDetails'] = ''
                        progress.update(main_task, advance=1)
                        continue

                    # Detect platform
                    platform = detect_platform(url)
                    platform_emoji = get_platform_emoji(platform)

                    # Update progress description
                    progress.update(main_task,
                        description=f"[cyan]{platform_emoji} 抓取: {job_title}[/cyan]")

                    # Route to appropriate fetcher based on platform
                    if platform == 'linkedin':
                        details = self._fetch_linkedin_job(url, progress)
                    elif platform == 'seek':
                        details = self._fetch_seek_job(url, progress)
                    else:
                        details = self._fetch_generic_job(url, progress)

                    job['jobDetails'] = details

                    # Clean other fields
                    for field in ['jobTitle', 'jobAdvertiser']:
                        if field in job and isinstance(job[field], str):
                            job[field] = clean_unicode_text(job[field])

                    progress.update(main_task, advance=1)

                    # Add delay for LinkedIn to prevent blocking
                    if platform == 'linkedin':
                        self.linkedin_fetcher.add_random_delay()

            finally:
                # Clean up LinkedIn resources
                self.linkedin_fetcher.cleanup()

        # Save results
        self.file_service.write_json("processed", output_filename, jobs)
        self.console.print(f"[bold green]成功处理 {len(jobs)} 个职位[/bold green]")
        self.console.print(f"[bold blue]结果已保存到 'processed/{output_filename}'[/bold blue]")

    def _display_platform_analysis(self, stats: Dict[str, int]):
        """Display platform analysis results"""
        self.console.print("\n[bold cyan]📊 平台分析结果:[/bold cyan]")

        for platform, count in stats.items():
            if count > 0:
                emoji = get_platform_emoji(platform)
                platform_name = platform.title()
                self.console.print(f"  {emoji} {platform_name}: {count} 个职位")

        total = sum(stats.values())
        self.console.print(f"[bold blue]🚀 总计: {total} 个职位，启动混合模式处理[/bold blue]\n")

    def _fetch_linkedin_job(self, url: str, progress) -> str:
        """Fetch LinkedIn job details"""
        try:
            return self.linkedin_fetcher.fetch_job_details(url)
        except Exception as e:
            progress.log(f"[red]LinkedIn抓取失败: {e}[/red]")
            return f"LinkedIn抓取失败: {str(e)}"

    def _fetch_seek_job(self, url: str, progress) -> str:
        """Fetch Seek job details"""
        try:
            return self.seek_fetcher.fetch_job_details(url)
        except Exception as e:
            progress.log(f"[red]Seek抓取失败: {e}[/red]")
            return f"Seek抓取失败: {str(e)}"

    def _fetch_generic_job(self, url: str, progress) -> str:
        """Fetch job details for unknown platforms using basic requests"""
        try:
            progress.log(f"[yellow]使用通用方法抓取: {url}[/yellow]")
            resp = requests.get(url, headers=HEADERS, timeout=10)
            resp.raise_for_status()

            soup = BeautifulSoup(resp.text, 'html.parser')

            # Try to extract text content
            text_content = soup.get_text(separator=' ', strip=True)
            return clean_unicode_text(text_content[:2000])  # Limit to first 2000 chars

        except Exception as e:
            progress.log(f"[red]通用抓取失败: {e}[/red]")
            return f"通用抓取失败: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="Fetch job details from a job list file.")
    parser.add_argument('--input', type=str, required=True, help='Input job list JSON file name (e.g., joblist_0619.json)')
    parser.add_argument('--output', type=str, required=True, help='Output file name for processed jobs (e.g., joblist_0619_details.json)')
    args = parser.parse_args()

    path_manager = PathManager()
    file_service = FileService(path_manager)
    fetcher = JobFetcher(file_service)
    
    fetcher.fetch_job_details(args.input, args.output)

if __name__ == "__main__":
    main()