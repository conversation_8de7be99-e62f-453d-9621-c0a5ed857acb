# /Users/<USER>/Desktop/aus_job/job_seeker_cli/scripts/crawlers/job_search_crawler.py
# 职位搜索爬虫：统一的多平台职位搜索接口
# 此文件整合LinkedIn和Seek的搜索功能，使用默认配置简化用户操作

from datetime import datetime
from typing import Dict, List, Any, Optional

from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.config.crawler_defaults import DEFAULT_SEARCH_PARAMS, SEEK_CONFIG

class JobSearchCrawler:
    """统一的职位搜索爬虫 - 使用默认配置"""

    def __init__(self, file_service: FileService):
        self.file_service = file_service

    async def search_with_defaults(self, platform: str) -> Optional[str]:
        """使用默认配置搜索职位并保存结果"""

        # 获取默认搜索参数
        keywords = DEFAULT_SEARCH_PARAMS["keywords"]
        location = DEFAULT_SEARCH_PARAMS["location"]

        if platform == "all":
            # 全平台搜索
            all_jobs = []

            # LinkedIn搜索
            linkedin_pages = DEFAULT_SEARCH_PARAMS["linkedin_pages"]
            linkedin_jobs = await self._search_linkedin(keywords, location, linkedin_pages)
            if linkedin_jobs:
                all_jobs.extend(linkedin_jobs)

            # Seek搜索
            seek_jobs = await self._search_seek()
            if seek_jobs:
                all_jobs.extend(seek_jobs)

            jobs = all_jobs
            platform_name = "multi_platform"

        elif platform == "linkedin":
            pages = DEFAULT_SEARCH_PARAMS["linkedin_pages"]
            jobs = await self._search_linkedin(keywords, location, pages)
            platform_name = "linkedin"

        elif platform == "seek":
            jobs = await self._search_seek()
            platform_name = "seek"

        else:
            print(f"❌ 不支持的平台: {platform}")
            return None

        if not jobs:
            print("❌ 未找到任何职位")
            return None

        # 保存结果
        return self._save_search_results(jobs, platform_name)

    async def _search_linkedin(self, keywords: str, location: str, pages: int) -> List[Dict[str, Any]]:
        """LinkedIn搜索"""
        print(f"🔗 开始LinkedIn搜索: {keywords} @ {location}")

        try:
            # 构建LinkedIn搜索URL
            search_url = self._build_linkedin_url(keywords, location)

            # 使用现有的LinkedIn爬虫逻辑
            from job_seeker_cli.scripts.crawlers.linkedin_crawler import LinkedInJobsCrawler

            crawler = LinkedInJobsCrawler(
                search_url=search_url,
                headless=False,  # 调试模式：关闭无头浏览器
                pages_to_crawl=pages
            )

            jobs = await crawler.run()
            print(f"✅ LinkedIn搜索完成，找到 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            print(f"❌ LinkedIn搜索失败: {e}")
            return []

    async def _search_seek(self) -> List[Dict[str, Any]]:
        """Seek职位搜索 - 使用独立的Seek爬虫"""
        print(f"🔍 抓取Seek首页职位...")

        try:
            # 使用独立的Seek爬虫逻辑
            from job_seeker_cli.scripts.crawlers.seek_crawler import SeekJobsCrawler

            crawler = SeekJobsCrawler(file_service=self.file_service)

            jobs = await crawler.run()
            print(f"✅ Seek搜索完成，找到 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            print(f"❌ Seek搜索失败: {e}")
            return []

    def _build_linkedin_url(self, keywords: str, location: str) -> str:
        """构建LinkedIn搜索URL"""
        import urllib.parse

        # URL编码关键词和地点
        encoded_keywords = urllib.parse.quote(keywords)
        encoded_location = urllib.parse.quote(location)

        # 构建LinkedIn搜索URL
        base_url = "https://www.linkedin.com/jobs/search/"
        params = f"?keywords={encoded_keywords}&location={encoded_location}&origin=SWITCH_SEARCH_VERTICAL"

        return base_url + params

    def _save_search_results(self, jobs: List[Dict[str, Any]], platform: str) -> str:
        """保存搜索结果"""
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{platform}_jobs_{timestamp}.json"

        # 保存到input目录 (直接保存数组，与mock.json格式一致)
        try:
            self.file_service.write_json("input", filename, jobs)
            print(f"✅ 搜索结果已保存: data/input/{filename}")
            print(f"📊 格式: 标准JSON数组，包含 {len(jobs)} 个职位")
            print(f"🏷️ 字段: jobTitle, jobAdvertiser, recommendedJobLink, source")
            return filename

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None