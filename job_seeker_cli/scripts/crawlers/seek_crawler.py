# /Users/<USER>/Desktop/aus_job/job_seeker_cli/scripts/crawlers/seek_crawler.py
# This script implements a simplified crawler for Seek.com.au.
# Its purpose is to extract job listings from the main job search page.

import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, BrowserProfiler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from crawl4ai.async_configs import CacheMode
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.services.path_manager import PathManager

class SeekJobsCrawler:
    """
    A simplified crawler for Seek.com.au that extracts job listings
    from a single, fixed URL without handling pagination or complex configurations.
    """

    def __init__(self, file_service: FileService, headless: bool = False, profile_name: str = "seek-browser-profile"):
        """
        Initializes the SeekJobsCrawler.

        Args:
            file_service: An instance of FileService for handling file operations.
            headless: Whether to run the browser in headless mode.
            profile_name: The name of the browser profile to use.
        """
        self.search_url = "https://www.seek.com.au/"
        self.file_service = file_service
        self.jobs = []
        self.headless = headless
        self.profile_name = profile_name

    async def setup_browser_profile(self) -> str:
        """Sets up the browser profile for Seek, creating one if it doesn't exist."""
        print("为Seek.com.au设置浏览器配置文件...")

        path_manager = PathManager()
        profiles_dir = os.path.join(path_manager.get_path("base"), "browser_profiles")
        os.makedirs(profiles_dir, exist_ok=True)

        profiler = BrowserProfiler()

        existing_profiles = profiler.list_profiles()
        profile_path = None

        for profile in existing_profiles:
            if profile['name'] == self.profile_name:
                profile_path = profile['path']
                print(f"使用现有配置文件: {self.profile_name} 位于 {profile_path}")
                break

        if not profile_path:
            print(f"创建新的浏览器配置文件: {self.profile_name} 位于 {profiles_dir}")
            print("请在打开的浏览器窗口中登录Seek.com.au...")
            print("完成后，在终端按'q'保存配置文件。")

            os.environ["CRAWL4AI_PROFILES_DIR"] = profiles_dir
            profile_path = await profiler.create_profile(
                profile_name=self.profile_name
            )

            print(f"配置文件保存在: {profile_path}")

        return profile_path

    async def extract_job_info(self) -> List[Dict[str, Any]]:
        """
        Extracts job information from the Seek job search results page.

        Returns:
            A list of dictionaries, where each dictionary represents a job listing.
        """
        profile_path = await self.setup_browser_profile()


        # 定义Seek职位列表的提取模式
        # 基于 mock.json 的格式
        schema = {
            "name": "Seek Job Listings",
            "baseSelector": "div[data-search-sol-meta]",
            "fields": [
                {
                    "name": "jobTitle",
                    "selector": '[data-automation="jobTitle"]',
                    "type": "text"
                },
                {
                    "name": "jobAdvertiser",
                    "selector": '[data-automation="jobAdvertiser"]',
                    "type": "text"
                },
                {
                    "name": "recommendedJobLink",
                    "selector": 'a[data-automation^="recommendedJobLink"]',
                    "type": "attribute",
                    "attribute": "href"
                }
            ]
        }

        # 爬虫配置，无需滚动或懒加载处理
        browser_config = BrowserConfig(
            headless=self.headless,
            use_managed_browser=True,
            user_data_dir=profile_path,
            browser_type="chromium",
            verbose=True
        )
        crawl_config = CrawlerRunConfig(
            wait_for='div[data-search-sol-meta]', # 等待职位卡片加载
            page_timeout=120000,  # 2分钟页面超时
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(schema, verbose=True),
            verbose=True
        )

        print(f"开始从以下链接爬取Seek职位: {self.search_url}")

        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=self.search_url, config=crawl_config)

                if not result.success:
                    print(f"爬取 {self.search_url} 时出错: {result.error_message}")
                    return []

                print("成功加载页面。正在提取职位信息...")

                if not hasattr(result, 'extracted_content') or not result.extracted_content:
                    print("未找到提取内容。提取可能失败。")
                    # 保存HTML以便检查
                    path_manager = PathManager()
                    debug_dir = path_manager.get_path("data")
                    debug_file = os.path.join(debug_dir, "seek_debug.html")
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(result.html)
                    print(f"已将HTML内容保存至 {debug_file} 用于调试")
                    return []

                try:
                    job_data = json.loads(result.extracted_content)

                    # Post-process the data to ensure links are absolute and add source
                    for job in job_data:
                        if job.get('recommendedJobLink') and job['recommendedJobLink'].startswith('/'):
                            job['recommendedJobLink'] = f"https://www.seek.com.au{job['recommendedJobLink']}"
                        job['source'] = 'seek'

                    print(f"成功提取 {len(job_data)} 个职位列表")
                    return job_data
                except json.JSONDecodeError as e:
                    print(f"解析提取内容时出错: {e}")
                    print(f"原始提取内容: {result.extracted_content[:200]}...")
                    return []
        except Exception as e:
            print(f"爬取过程中出错: {e}")
            return []

    def save_results(self, jobs: List[Dict[str, Any]]):
        """
        Saves the extracted job information to a JSON file and a Markdown summary.
        The files are saved in the 'data/input' directory.
        """
        if not jobs:
            print("No jobs to save.")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"seek_jobs_{timestamp}"
        
        # Save JSON file using FileService
        json_filename = f"{base_filename}.json"
        self.file_service.write_json("input", json_filename, jobs)
        print(f"Saved {len(jobs)} job listings to data/input/{json_filename}")

        # Save Markdown summary using FileService
        md_filename = f"seek_jobs_summary_{timestamp}.md"
        summary_content = [
            f"# Seek Job Crawl Results\n",
            f"**Crawl Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Search URL:** {self.search_url}",
            f"**Total Jobs Found:** {len(jobs)}\n",
            "## Job Listings\n"
        ]
        for i, job in enumerate(jobs, 1):
            title = job.get('jobTitle', 'Unknown Title')
            advertiser = job.get('jobAdvertiser', 'Unknown Advertiser')
            link = job.get('recommendedJobLink', '#')
            summary_content.append(f"{i}. **{title}** at {advertiser} - [View Job]({link})")

        self.file_service.write_file("input", md_filename, "\n".join(summary_content))
        print(f"Summary saved to data/input/{md_filename}")


    async def run(self):
        """运行Seek职位爬虫并保存结果。"""
        print(f"🚀 开始爬取任务...")
        jobs = await self.extract_job_info()

        print(f"\n--- 🎉 爬取任务完成！---")
        print(f"总共提取到 {len(jobs)} 条职位信息。")
        if jobs:
            self.save_results(jobs)
        self.jobs = jobs
        return jobs

async def main():
    """Main function to test the SeekJobsCrawler."""
    path_manager = PathManager()
    file_service = FileService(path_manager)
    crawler = SeekJobsCrawler(file_service=file_service, headless=False)
    await crawler.run()

if __name__ == "__main__":
    asyncio.run(main())
