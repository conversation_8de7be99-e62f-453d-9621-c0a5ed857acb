# /Users/<USER>/Desktop/aus_job/job_seeker_cli/scripts/crawlers/seek_crawler.py
# This script implements a simplified crawler for Seek.com.au.
# Its purpose is to extract job listings from the main job search page.

import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, BrowserProfiler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from services.file_service import FileService
from job_seeker_cli.services.path_manager import PathManager

class SeekJobsCrawler:
    """
    A simplified crawler for Seek.com.au that extracts job listings
    from a single, fixed URL without handling pagination or complex configurations.
    """

    def __init__(self, file_service: FileService, headless: bool = False, profile_name: str = "seek-browser-profile"):
        """
        Initializes the SeekJobsCrawler.

        Args:
            file_service: An instance of FileService for handling file operations.
            headless: Whether to run the browser in headless mode.
            profile_name: The name of the browser profile to use.
        """
        self.search_url = "https://www.seek.com.au/"
        self.file_service = file_service
        self.jobs = []
        self.headless = headless
        self.profile_name = profile_name

    async def setup_browser_profile(self) -> str:
        """Sets up the browser profile for Seek, creating one if it doesn't exist."""
        print("Setting up browser profile for Seek.com.au...")
        
        path_manager = PathManager()
        profiles_dir = os.path.join(path_manager.get_project_root(), "browser_profiles")
        os.makedirs(profiles_dir, exist_ok=True)
        
        profiler = BrowserProfiler(profiles_dir=profiles_dir)
        
        existing_profiles = profiler.list_profiles()
        profile_path = None
        
        for profile in existing_profiles:
            if profile['name'] == self.profile_name:
                profile_path = profile['path']
                print(f"Using existing profile: {self.profile_name} at {profile_path}")
                break
        
        if not profile_path:
            print(f"Creating new browser profile: {self.profile_name} at {profiles_dir}")
            print("Please log in to Seek.com.au in the browser window that opens.")
            print("When you are done, press 'q' in the terminal to save the profile.")
            
            profile_path = await profiler.create_profile(
                profile_name=self.profile_name
            )
            
            print(f"Profile saved at: {profile_path}")
        
        return profile_path

    async def extract_job_info(self) -> List[Dict[str, Any]]:
        """
        Extracts job information from the Seek job search results page.

        Returns:
            A list of dictionaries, where each dictionary represents a job listing.
        """
        profile_path = await self.setup_browser_profile()
        print(f"Starting to crawl Seek jobs from: {self.search_url}")

        # Define the extraction schema based on the reference script
        # and unified field names.
        schema = {
            "name": "Seek Job Listings",
            "baseSelector": "div[data-search-sol-meta]",
            "fields": [
                {
                    "name": "jobTitle",
                    "selector": "a[data-automation='job-title']",
                    "type": "text"
                },
                {
                    "name": "jobAdvertiser",
                    "selector": "a[data-automation='job-advertiser-name']",
                    "type": "text"
                },
                {
                    "name": "recommendedJobLink",
                    "selector": "a[data-automation='job-title']",
                    "type": "attribute",
                    "attribute": "href"
                }
            ]
        }

        # Simplified crawler configuration
        browser_config = BrowserConfig(
            headless=self.headless,
            browser_type="chromium",
            user_data_dir=profile_path
        )
        crawl_config = CrawlerRunConfig(
            wait_for="div[data-search-sol-meta]",
            page_timeout=120000,
            extraction_strategy=JsonCssExtractionStrategy(schema, verbose=True),
            verbose=True
        )

        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=self.search_url, config=crawl_config)

                if not result.success or not result.extracted_content:
                    print(f"Failed to crawl {self.search_url}: {result.error_message}")
                    return []

                job_data = json.loads(result.extracted_content)

                # Post-process the data to ensure links are absolute
                for job in job_data:
                    if job.get('recommendedJobLink') and job['recommendedJobLink'].startswith('/'):
                        job['recommendedJobLink'] = f"https://www.seek.com.au{job['recommendedJobLink']}"
                    job['source'] = 'seek'

                print(f"Successfully extracted {len(job_data)} job listings from Seek.")
                return job_data
        except Exception as e:
            print(f"An error occurred during the crawling process: {e}")
            return []

    def save_results(self, jobs: List[Dict[str, Any]]):
        """
        Saves the extracted job information to a JSON file and a Markdown summary.
        The files are saved in the 'data/input' directory.
        """
        if not jobs:
            print("No jobs to save.")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"seek_jobs_{timestamp}"
        
        # Save JSON file using FileService
        json_filename = f"{base_filename}.json"
        self.file_service.write_json("input", json_filename, jobs)
        print(f"Saved {len(jobs)} job listings to data/input/{json_filename}")

        # Save Markdown summary using FileService
        md_filename = f"seek_jobs_summary_{timestamp}.md"
        summary_content = [
            f"# Seek Job Crawl Results\n",
            f"**Crawl Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Search URL:** {self.search_url}",
            f"**Total Jobs Found:** {len(jobs)}\n",
            "## Job Listings\n"
        ]
        for i, job in enumerate(jobs, 1):
            title = job.get('jobTitle', 'Unknown Title')
            advertiser = job.get('jobAdvertiser', 'Unknown Advertiser')
            link = job.get('recommendedJobLink', '#')
            summary_content.append(f"{i}. **{title}** at {advertiser} - [View Job]({link})")

        self.file_service.write_file("input", md_filename, "\n".join(summary_content))
        print(f"Summary saved to data/input/{md_filename}")


    async def run(self):
        """
        Runs the complete Seek job crawling process.
        """
        print("🚀 Starting Seek job crawler...")
        self.jobs = await self.extract_job_info()
        if self.jobs:
            self.save_results(self.jobs)
        print("--- 🎉 Seek job crawl finished! ---")
        return self.jobs

async def main():
    """Main function to test the SeekJobsCrawler."""
    path_manager = PathManager()
    file_service = FileService(path_manager)
    crawler = SeekJobsCrawler(file_service=file_service, headless=False)
    await crawler.run()

if __name__ == "__main__":
    asyncio.run(main())
