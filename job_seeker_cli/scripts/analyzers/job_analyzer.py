import json
import requests
import re
import argparse
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console

from job_seeker_cli.services.path_manager import PathManager
from job_seeker_cli.services.file_service import FileService

from job_seeker_cli.services.api_client import ApiClient

class JobAnalyzer:
    def __init__(self, file_service: FileService, api_client: ApiClient):
        self.file_service = file_service
        self.api_client = api_client
        self.console = Console()

    def _get_match_evaluation(self, resume: str, job_description: str) -> Optional[Dict[str, Any]]:
        """Call the API to get a job match evaluation."""
        prompt_sys = (
            f"You are a career matching assistant. Candidate resume:\\n{resume}\\n"
            "Evaluate the fit against the job description and output ONLY a JSON object with fields 'score' (integer 0-100) and 'evaluation' (≤200 characters summarizing key reasons)."
        )
        prompt_user = f"Job description:\\n{job_description}"
        body = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'system', 'content': prompt_sys},
                {'role': 'user', 'content': prompt_user}
            ]
        }
        
        data = self.api_client.post(body)
        if not data:
            return None
            
        content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
        if not content:
            return None

        try:
            # First, try to parse the content as JSON
            return json.loads(content)
        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract score with regex as a fallback
            print("Could not parse API response as JSON. Trying regex fallback.")
            try:
                score_match = re.search(r'(\d{1,3})', content)
                score = int(score_match.group(1)) if score_match else 0
                evaluation = content.strip().replace('\\n', ' ')[:200]
                return {"score": score, "evaluation": evaluation}
            except Exception as e:
                print(f"Regex fallback also failed: {e}")
                return None

    def analyze_jobs(self, input_filename: str, resume_filename: str, output_filename: str):
        """
        Analyzes job descriptions against a resume to find suitable matches.

        Args:
            input_filename (str): The name of the processed jobs JSON file.
            resume_filename (str): The name of the resume file in 'personal' directory.
            output_filename (str): The name for the output JSON file in 'target_jobs'.
        """
        jobs: Optional[List[Dict[str, Any]]] = self.file_service.read_json("processed", input_filename)
        if not jobs:
            self.console.print(f"[bold red]Could not read or parse {input_filename}[/bold red]")
            return

        resume = self.file_service.read_file("personal", resume_filename)
        if not resume:
            self.console.print(f"[bold red]Could not read resume file {resume_filename}[/bold red]")
            return

        target_jobs = []
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(bar_width=40),
            TextColumn("[bold green]{task.completed}/{task.total}[/bold green]"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task("[cyan]Analyzing jobs[/cyan]", total=len(jobs))
            for i, job in enumerate(jobs):
                job_title = job.get('jobTitle', 'N/A')
                progress.update(task, description=f"[cyan]Analyzing job: {job_title}[/cyan]")
                
                job_description = job.get('jobDetails', 'No job description available')
                
                evaluation = self._get_match_evaluation(resume, job_description)
                if not evaluation:
                    progress.update(task, advance=1)
                    continue

                score = evaluation.get('score', 0)
                if score >= 80:
                    entry = job.copy()
                    entry['match_score'] = score
                    entry['evaluation'] = evaluation.get('evaluation', '').strip()
                    target_jobs.append(entry)
                    progress.log(f"  -> Match found! Score: {score} for {job_title}")
                
                progress.update(task, advance=1)

        if target_jobs:
            self.file_service.write_json("target_jobs", output_filename, target_jobs)
            self.console.print(f"[bold green]Found {len(target_jobs)} suitable jobs.[/bold green]")
            self.console.print(f"[bold blue]Results saved to 'target_jobs/{output_filename}'[/bold blue]")
        else:
            self.console.print("[bold yellow]No suitable jobs found with a score of 80 or higher.[/bold yellow]")

def main():
    parser = argparse.ArgumentParser(description="Analyze job descriptions against a resume.")
    parser.add_argument('--input', type=str, required=True, help='Input processed job file name (e.g., joblist_0619_details.json)')
    parser.add_argument('--resume', type=str, default='qiansui_cv.md', help='Resume file name in personal directory.')
    
    args = parser.parse_args()

    # Generate a date-stamped output file name
    today = datetime.now().strftime('%m%d')
    output_filename = f'target_jobs_{today}.json'

    # Load API key from environment
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        raise RuntimeError('DEEPSEEK_API_KEY not set. Please define it in .env or environment variables.')

    path_manager = PathManager()
    file_service = FileService(path_manager)
    api_client = ApiClient(api_key=api_key, api_url='https://api.deepseek.com/chat/completions')
    analyzer = JobAnalyzer(file_service, api_client)
    
    analyzer.analyze_jobs(args.input, args.resume, output_filename)

if __name__ == "__main__":
    # You need to have DEEPSEEK_API_KEY in your environment to run this
    # Example: DEEPSEEK_API_KEY="your_key" python job_seeker_cli/scripts/job_analyzer.py --input joblist_0619_details.json
    main()
